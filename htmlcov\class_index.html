<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">82%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 20:16 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>154</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="120 154">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t11">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t11"><data value='ConfigLoader'>ConfigLoader</data></a></td>
                <td>49</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="41 49">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="24 25">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e___init___py.html">src\services\__init__.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t31">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t31"><data value='MoviePreprocessor'>MoviePreprocessor</data></a></td>
                <td>163</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="128 163">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="38 51">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t18">src\services\movie_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t18"><data value='MovieService'>MovieService</data></a></td>
                <td>60</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="51 60">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html">src\services\movie_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t21">src\services\recommendation_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t21"><data value='RecommendationService'>RecommendationService</data></a></td>
                <td>137</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="113 137">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html">src\services\recommendation_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>677</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="553 677">82%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 20:16 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
