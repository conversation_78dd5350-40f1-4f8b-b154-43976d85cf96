{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.1", "globals": "93f24b050990bc21bfe0608e482ee5a2", "files": {"z_145eef247bfb46b6___init___py": {"hash": "68f6f5cffa599af50e6865e0815d5be6", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_app_py": {"hash": "5f5aa4380a8b3cd2eb9ed4bf082f2085", "index": {"url": "z_145eef247bfb46b6_app_py.html", "file": "src\\app.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 154, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997___init___py": {"hash": "25c6a1fd2d043824eb572e2662b2f074", "index": {"url": "z_b695c9c33e1e1997___init___py.html", "file": "src\\config\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997_config_loader_py": {"hash": "06163374732283596e01e7289f0b905a", "index": {"url": "z_b695c9c33e1e1997_config_loader_py.html", "file": "src\\config\\config_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_33ea1954bbe60e2e___init___py": {"hash": "39598289f72254e54f514d23bbc8bb9b", "index": {"url": "z_33ea1954bbe60e2e___init___py.html", "file": "src\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_33ea1954bbe60e2e_movie_preprocessor_py": {"hash": "371f246cb443ffc9c986c610a3d04b9f", "index": {"url": "z_33ea1954bbe60e2e_movie_preprocessor_py.html", "file": "src\\services\\movie_preprocessor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 214, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_33ea1954bbe60e2e_movie_service_py": {"hash": "bb2cc8d29708812e8e98f02d71f4f701", "index": {"url": "z_33ea1954bbe60e2e_movie_service_py.html", "file": "src\\services\\movie_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_33ea1954bbe60e2e_recommendation_service_py": {"hash": "067129da91e6aecd705508026c67f5ae", "index": {"url": "z_33ea1954bbe60e2e_recommendation_service_py.html", "file": "src\\services\\recommendation_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}