["tests/test_app.py::TestFeatureToggles::test_all_features_disabled", "tests/test_app.py::TestFeatureToggles::test_selective_feature_enabling", "tests/test_app.py::TestFlaskApp::test_404_error_handler", "tests/test_app.py::TestFlaskApp::test_500_error_handler", "tests/test_app.py::TestFlaskApp::test_api_available_genres", "tests/test_app.py::TestFlaskApp::test_api_genre_recommendations", "tests/test_app.py::TestFlaskApp::test_api_movie_recommendations", "tests/test_app.py::TestFlaskApp::test_api_movie_recommendations_basic", "tests/test_app.py::TestFlaskApp::test_api_popular", "tests/test_app.py::TestFlaskApp::test_api_recommendation_stats", "tests/test_app.py::TestFlaskApp::test_api_search_get_disabled", "tests/test_app.py::TestFlaskApp::test_api_search_get_enabled", "tests/test_app.py::TestFlaskApp::test_api_search_post_enabled", "tests/test_app.py::TestFlaskApp::test_api_search_post_missing_title", "tests/test_app.py::TestFlaskApp::test_api_search_suggestions", "tests/test_app.py::TestFlaskApp::test_api_search_suggestions_short_query", "tests/test_app.py::TestFlaskApp::test_create_app", "tests/test_app.py::TestFlaskApp::test_genre_recommendations", "tests/test_app.py::TestFlaskApp::test_health_check", "tests/test_app.py::TestFlaskApp::test_home_route", "tests/test_app.py::TestFlaskApp::test_movie_recommendations", "tests/test_app.py::TestFlaskApp::test_movie_recommendations_no_results", "tests/test_app.py::TestFlaskApp::test_popular_movies_disabled", "tests/test_app.py::TestFlaskApp::test_popular_movies_enabled", "tests/test_app.py::TestFlaskApp::test_recommendations_page_disabled", "tests/test_app.py::TestFlaskApp::test_recommendations_page_enabled", "tests/test_app.py::TestFlaskApp::test_search_movie_empty_title", "tests/test_app.py::TestFlaskApp::test_search_movie_not_found", "tests/test_app.py::TestFlaskApp::test_search_movie_success", "tests/test_app.py::TestFlaskAppIntegration::test_api_workflow", "tests/test_app.py::TestFlaskAppIntegration::test_full_search_workflow", "tests/test_config_loader.py::TestConfigLoader::test_empty_key_path", "tests/test_config_loader.py::TestConfigLoader::test_environment_variable_boolean_override", "tests/test_config_loader.py::TestConfigLoader::test_environment_variable_override", "tests/test_config_loader.py::TestConfigLoader::test_get_api_key", "tests/test_config_loader.py::TestConfigLoader::test_get_api_url", "tests/test_config_loader.py::TestConfigLoader::test_get_app_config", "tests/test_config_loader.py::TestConfigLoader::test_get_default_config", "tests/test_config_loader.py::TestConfigLoader::test_get_nested_key", "tests/test_config_loader.py::TestConfigLoader::test_get_popular_movies", "tests/test_config_loader.py::TestConfigLoader::test_get_search_suggestions", "tests/test_config_loader.py::TestConfigLoader::test_get_simple_key", "tests/test_config_loader.py::TestConfigLoader::test_get_with_default", "tests/test_config_loader.py::TestConfigLoader::test_get_without_default", "tests/test_config_loader.py::TestConfigLoader::test_init_with_custom_path", "tests/test_config_loader.py::TestConfigLoader::test_init_with_default_path", "tests/test_config_loader.py::TestConfigLoader::test_invalid_key_path", "tests/test_config_loader.py::TestConfigLoader::test_is_feature_enabled_false", "tests/test_config_loader.py::TestConfigLoader::test_is_feature_enabled_nonexistent", "tests/test_config_loader.py::TestConfigLoader::test_is_feature_enabled_true", "tests/test_config_loader.py::TestConfigLoader::test_load_config_file_not_found", "tests/test_config_loader.py::TestConfigLoader::test_load_config_invalid_json", "tests/test_config_loader.py::TestConfigLoader::test_load_config_success", "tests/test_config_loader.py::TestConfigLoader::test_none_key_path", "tests/test_config_loader.py::TestConfigLoader::test_save_config", "tests/test_config_loader.py::TestConfigLoader::test_save_config_error", "tests/test_config_loader.py::TestConfigValidation::test_malformed_config_structure", "tests/test_config_loader.py::TestConfigValidation::test_missing_required_api_key", "tests/test_config_loader.py::TestConvenienceFunctions::test_get_api_key_function", "tests/test_config_loader.py::TestConvenienceFunctions::test_get_api_url_function", "tests/test_config_loader.py::TestConvenienceFunctions::test_get_config_function", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_check_processed_data_exists", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_clean_text", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_clean_text_empty_input", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_create_combined_features", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_create_combined_features_missing_columns", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_create_similarity_matrix", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_create_tfidf_matrix", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_download_nltk_data", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_init_success", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_lemmatize_text", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_load_data_file_not_found", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_load_data_success", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_process_all_success", "tests/test_movie_preprocessor.py::TestMoviePreprocessor::test_save_processed_data", "tests/test_movie_preprocessor.py::TestMoviePreprocessorEdgeCases::test_empty_dataframe", "tests/test_movie_preprocessor.py::TestMoviePreprocessorEdgeCases::test_invalid_tfidf_params", "tests/test_movie_preprocessor.py::TestPreprocessMoviesFunction::test_preprocess_movies_force_reprocess", "tests/test_movie_preprocessor.py::TestPreprocessMoviesFunction::test_preprocess_movies_no_force", "tests/test_movie_service.py::TestMovieService::test_format_movie_data", "tests/test_movie_service.py::TestMovieService::test_format_movie_data_missing_fields", "tests/test_movie_service.py::TestMovieService::test_get_movie_by_imdb_id_success", "tests/test_movie_service.py::TestMovieService::test_get_movie_details_not_found", "tests/test_movie_service.py::TestMovieService::test_get_movie_details_success", "tests/test_movie_service.py::TestMovieService::test_get_movie_full_details_api_error", "tests/test_movie_service.py::TestMovieService::test_get_movie_full_details_success", "tests/test_movie_service.py::TestMovieService::test_get_popular_movies_success", "tests/test_movie_service.py::TestMovieService::test_init_missing_api_key", "tests/test_movie_service.py::TestMovieService::test_init_placeholder_api_key", "tests/test_movie_service.py::TestMovieService::test_init_success", "tests/test_movie_service.py::TestMovieService::test_retry_exhausted", "tests/test_movie_service.py::TestMovieService::test_retry_mechanism", "tests/test_movie_service.py::TestMovieService::test_search_movies_by_title_no_results", "tests/test_movie_service.py::TestMovieService::test_search_movies_by_title_success", "tests/test_movie_service.py::TestMovieService::test_timeout_handling", "tests/test_movie_service.py::TestMovieServiceIntegration::test_full_workflow", "tests/test_recommendation_service.py::TestRecommendationService::test_get_available_genres", "tests/test_recommendation_service.py::TestRecommendationService::test_get_available_genres_not_loaded", "tests/test_recommendation_service.py::TestRecommendationService::test_get_dataset_stats", "tests/test_recommendation_service.py::TestRecommendationService::test_get_dataset_stats_not_loaded", "tests/test_recommendation_service.py::TestRecommendationService::test_get_enhanced_recommendations", "tests/test_recommendation_service.py::TestRecommendationService::test_get_enhanced_recommendations_feature_disabled", "tests/test_recommendation_service.py::TestRecommendationService::test_get_movie_recommendations_movie_not_found", "tests/test_recommendation_service.py::TestRecommendationService::test_get_movie_recommendations_not_loaded", "tests/test_recommendation_service.py::TestRecommendationService::test_get_movie_recommendations_success", "tests/test_recommendation_service.py::TestRecommendationService::test_get_recommendations_by_genre", "tests/test_recommendation_service.py::TestRecommendationService::test_get_recommendations_by_genre_not_found", "tests/test_recommendation_service.py::TestRecommendationService::test_init_success", "tests/test_recommendation_service.py::TestRecommendationService::test_load_processed_data_missing_files", "tests/test_recommendation_service.py::TestRecommendationService::test_load_processed_data_success", "tests/test_recommendation_service.py::TestRecommendationServiceEdgeCases::test_case_insensitive_movie_search", "tests/test_recommendation_service.py::TestRecommendationServiceEdgeCases::test_empty_similarity_matrix", "tests/test_recommendation_service.py::TestRecommendationServiceEdgeCases::test_similarity_threshold_filtering"]