<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">82%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 20:16 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t18">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t18"><data value='create_app'>create_app</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t33">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t33"><data value='register_routes'>register_routes</data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t37">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t37"><data value='home'>register_routes.home</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t43">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t43"><data value='search_movie'>register_routes.search_movie</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t63">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t63"><data value='popular_movies'>register_routes.popular_movies</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t76">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t76"><data value='api_search'>register_routes.api_search</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t85">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t85"><data value='api_search_post'>register_routes.api_search_post</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t98">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t98"><data value='api_search_suggestions'>register_routes.api_search_suggestions</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t133">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t133"><data value='api_popular'>register_routes.api_popular</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t150">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t150"><data value='recommendations_page'>register_routes.recommendations_page</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t162">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t162"><data value='movie_recommendations'>register_routes.movie_recommendations</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t182">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t182"><data value='genre_recommendations'>register_routes.genre_recommendations</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t202">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t202"><data value='api_movie_recommendations'>register_routes.api_movie_recommendations</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t222">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t222"><data value='api_genre_recommendations'>register_routes.api_genre_recommendations</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t237">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t237"><data value='api_available_genres'>register_routes.api_available_genres</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t249">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t249"><data value='api_recommendation_stats'>register_routes.api_recommendation_stats</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t258">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t258"><data value='health_check'>register_routes.health_check</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t273">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t273"><data value='not_found'>register_routes.not_found</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t280">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t280"><data value='internal_error'>register_routes.internal_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t286">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html#t286"><data value='main'>main</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html">src\app.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_app_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t14">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t14"><data value='init__'>ConfigLoader.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t30">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t30"><data value='load_config'>ConfigLoader.load_config</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t43">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t43"><data value='get_default_config'>ConfigLoader._get_default_config</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t68">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t68"><data value='get'>ConfigLoader.get</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t96">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t96"><data value='get_api_key'>ConfigLoader.get_api_key</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t100">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t100"><data value='get_api_url'>ConfigLoader.get_api_url</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t104">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t104"><data value='get_app_config'>ConfigLoader.get_app_config</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t113">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t113"><data value='is_feature_enabled'>ConfigLoader.is_feature_enabled</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t117">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t117"><data value='get_popular_movies'>ConfigLoader.get_popular_movies</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t121">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t121"><data value='get_search_suggestions'>ConfigLoader.get_search_suggestions</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t125">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t125"><data value='update_config'>ConfigLoader.update_config</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t148">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t148"><data value='save_config'>ConfigLoader.save_config</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t161">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t161"><data value='get_config'>get_config</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t165">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t165"><data value='get_api_key'>get_api_key</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t169">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t169"><data value='get_api_url'>get_api_url</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t173">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html#t173"><data value='is_feature_enabled'>is_feature_enabled</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html">src\config\config_loader.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_config_loader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e___init___py.html">src\services\__init__.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t37">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t37"><data value='init__'>MoviePreprocessor.__init__</data></a></td>
                <td>24</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="15 24">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t70">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t70"><data value='download_nltk_data'>MoviePreprocessor.download_nltk_data</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t86">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t86"><data value='lemmatize_text'>MoviePreprocessor.lemmatize_text</data></a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t116">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t116"><data value='load_data'>MoviePreprocessor.load_data</data></a></td>
                <td>19</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 19">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t155">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t155"><data value='create_sample_dataset'>MoviePreprocessor._create_sample_dataset</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t221">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t221"><data value='clean_text'>MoviePreprocessor.clean_text</data></a></td>
                <td>30</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="23 30">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t285">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t285"><data value='create_combined_features'>MoviePreprocessor.create_combined_features</data></a></td>
                <td>19</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="16 19">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t334">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t334"><data value='create_tfidf_matrix'>MoviePreprocessor.create_tfidf_matrix</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t364">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t364"><data value='create_similarity_matrix'>MoviePreprocessor.create_similarity_matrix</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t381">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t381"><data value='save_processed_data'>MoviePreprocessor.save_processed_data</data></a></td>
                <td>16</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="13 16">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t427">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t427"><data value='process_all'>MoviePreprocessor.process_all</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t460">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t460"><data value='check_processed_data_exists'>MoviePreprocessor.check_processed_data_exists</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t481">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html#t481"><data value='preprocess_movies'>preprocess_movies</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html">src\services\movie_preprocessor.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_preprocessor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>46</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="33 46">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t21">src\services\movie_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t21"><data value='init__'>MovieService.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t31">src\services\movie_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t31"><data value='get_movie_details'>MovieService.get_movie_details</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t46">src\services\movie_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t46"><data value='get_movie_full_details'>MovieService.get_movie_full_details</data></a></td>
                <td>20</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="17 20">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t102">src\services\movie_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t102"><data value='format_movie_data'>MovieService._format_movie_data</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t136">src\services\movie_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t136"><data value='search_movies_by_title'>MovieService.search_movies_by_title</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t183">src\services\movie_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t183"><data value='get_movie_by_imdb_id'>MovieService.get_movie_by_imdb_id</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t222">src\services\movie_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html#t222"><data value='get_popular_movies'>MovieService.get_popular_movies</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html">src\services\movie_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_movie_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t27">src\services\recommendation_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t27"><data value='init__'>RecommendationService.__init__</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t46">src\services\recommendation_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t46"><data value='load_processed_data'>RecommendationService.load_processed_data</data></a></td>
                <td>24</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="21 24">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t91">src\services\recommendation_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t91"><data value='get_movie_recommendations'>RecommendationService.get_movie_recommendations</data></a></td>
                <td>38</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="31 38">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t177">src\services\recommendation_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t177"><data value='get_recommendations_by_genre'>RecommendationService.get_recommendations_by_genre</data></a></td>
                <td>24</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="20 24">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t233">src\services\recommendation_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t233"><data value='get_enhanced_recommendations'>RecommendationService.get_enhanced_recommendations</data></a></td>
                <td>16</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="12 16">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t276">src\services\recommendation_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t276"><data value='get_available_genres'>RecommendationService.get_available_genres</data></a></td>
                <td>14</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="11 14">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t305">src\services\recommendation_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html#t305"><data value='get_dataset_stats'>RecommendationService.get_dataset_stats</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html">src\services\recommendation_service.py</a></td>
                <td class="name left"><a href="z_33ea1954bbe60e2e_recommendation_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>677</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="553 677">82%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-02 20:16 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
